{"name": "meet-hour", "version": "2.0.1", "description": "Meet Hour Video Conference App - React & React Native", "repository": {"type": "git", "url": "git://github.com/v-empower/Meethour-Web-MobileApps"}, "keywords": ["jingle", "webrtc", "xmpp", "browser"], "author": "", "readmeFilename": "README.md", "dependencies": {"@atlaskit/avatar": "^20.2.2", "@atlaskit/button": "15.1.4", "@atlaskit/checkbox": "12.0.0", "@atlaskit/dropdown-menu": "10.1.2", "@atlaskit/field-text": "11.0.4", "@atlaskit/field-text-area": "8.0.4", "@atlaskit/flag": "14.1.0", "@atlaskit/form": "^8.2.2", "@atlaskit/icon": "21.2.0", "@atlaskit/inline-dialog": "13.0.9", "@atlaskit/inline-message": "11.0.8", "@atlaskit/lozenge": "^10.1.2", "@atlaskit/modal-dialog": "11.2.4", "@atlaskit/multi-select": "15.0.5", "@atlaskit/spinner": "15.0.6", "@atlaskit/tabs": "12.1.2", "@atlaskit/textfield": "^5.1.4", "@atlaskit/theme": "11.0.2", "@atlaskit/toggle": "12.0.3", "@atlaskit/tooltip": "17.1.2", "@babel/plugin-transform-runtime": "^7.19.1", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@giphy/js-fetch-api": "4.7.1", "@giphy/js-types": "^5.1.0", "@giphy/react-components": "^8.0.0", "@giphy/react-native-sdk": "^2.3.0", "@jitsi/js-utils": "2.2.1", "@mantine/hooks": "^5.8.2", "@material-ui/core": "^4.12.4", "@material-ui/lab": "^4.0.0-alpha.61", "@microsoft/microsoft-graph-client": "1.1.0", "@mui/material": "^5.15.12", "@react-native-async-storage/async-storage": "^1.23.1", "@react-native-community/netinfo": "^11.1.0", "@react-native-google-signin/google-signin": "^10.1.0", "@react-native/normalize-color": "^2.0.0", "@react-native/normalize-colors": "^0.73.2", "@react-navigation/drawer": "^6.1.4", "@react-navigation/material-top-tabs": "6.6.13", "@react-navigation/native": "6.1.17", "@react-navigation/stack": "6.4.0", "@svgr/webpack": "6.3.1", "@tanstack/react-query": "^4.10.1", "@tanstack/react-query-devtools": "^4.10.1", "@xmldom/xmldom": "0.8.7", "meet-hour-react-interval-hook": "^1.0.0", "amplitude-js": "8.21.9", "axios": "1.7.5", "base64-js": "1.3.1", "bc-css-flags": "3.0.0", "clipboard-copy": "4.0.1", "core-js": "^3.25.5", "dropbox": "^10.7.0", "focus-visible": "5.1.0", "history": "^5.3.0", "i18n-iso-countries": "3.7.8", "i18next": "17.0.6", "i18next-browser-languagedetector": "3.0.1", "i18next-xhr-backend": "3.0.0", "jquery": "3.5.1", "jquery-contextmenu": "2.4.5", "jquery-i18next": "1.2.1", "jQuery-Impromptu": "github:trent<PERSON><PERSON><PERSON>/jQuery-Impromptu#v6.0.0", "js-md5": "0.6.1", "js-sha256": "^0.9.0", "jwt-decode": "2.2.0", "lib-meet-hour": "file:lib-meet-hour", "libflacjs": "github:mmig/libflac.js#93d37e7f811f01cf7d8b6a603e38bd3c3810907d", "lodash": "4.17.21", "markerjs2": "^2.29.4", "meet-hour-logger": "^1.0.0", "metismenujs": "^1.4.0", "metro-config": "^0.80.8", "moment": "^2.29.1", "moment-duration-format": "^2.2.2", "moment-timezone": "^0.5.40", "null-loader": "4.0.1", "olm": "https://packages.matrix.org/npm/olm/olm-3.2.1.tgz", "pixelmatch": "5.1.0", "punycode": "2.1.1", "react": "18.2.0", "react-chat-elements": "^12.0.6", "react-cookie-consent": "^9.0.0", "react-dom": "18.2.0", "react-emoji-render": "1.2.4", "react-error-boundary": "^3.1.4", "react-focus-lock": "2.5.1", "react-focus-on": "3.8.1", "react-google-recaptcha": "^2.1.0", "react-helmet": "^6.1.0", "react-i18next": "10.11.4", "react-jss": "^10.9.0", "react-leave-page-confirm": "^0.0.6", "react-linkify": "1.0.0-alpha", "react-meta-tags": "^1.0.1", "react-native": "^0.73.8", "react-native-add-calendar-event": "5.0.0", "react-native-animated-scroll-indicators": "^1.0.1", "react-native-background-timer": "2.4.1", "react-native-calendar-events": "2.2.0", "react-native-callstats": "3.70.1", "react-native-collapsible": "1.6.0", "react-native-default-preference": "1.4.4", "react-native-device-info": "10.9.0", "react-native-fast-image": "^8.6.3", "react-native-gesture-handler": "^2.16.2", "react-native-gif-search": "^1.3.2", "react-native-immersive-mode": "2.0.2", "react-native-keep-awake": "4.0.0", "react-native-linear-gradient": "2.8.3", "react-native-pager-view": "6.1.2", "react-native-paper": "4.12.6", "react-native-performance": "5.1.2", "react-native-reanimated": "^3.11.0", "react-native-safe-area-context": "^4.10.9", "react-native-screens": "^3.31.1", "react-native-sound": "0.11.2", "react-native-splash-screen": "3.3.0", "react-native-svg": "13.13.0", "react-native-svg-transformer": "1.2.0", "react-native-tab-view": "3.5.1", "react-native-url-polyfill": "1.2.0", "react-native-video": "https://**************/react-native-video/react-native-video#7c48ae7c8544b2b537fb60194e9620b9fcceae52", "react-native-watch-connectivity": "^1.0.4", "react-native-webrtc": "github:v-empower/react-native-webrtc#d3e37c186b9204c46956df8ec185da38096c4961", "react-native-webview": "13.8.7", "react-native-youtube-iframe": "2.2.1", "react-navigation": "^3.11.0", "react-query": "^3.39.2", "react-redux": "7.1.0", "react-responsive": "^9.0.0-beta.5", "react-speech-recognition": "^3.9.0", "react-textarea-autosize": "7.1.0", "react-toastify": "^9.0.8", "react-transition-group": "2.4.0", "react-window": "^1.8.6", "react-youtube": "7.13.1", "redux": "4.0.4", "redux-thunk": "2.2.0", "regenerator-runtime": "^0.13.9", "rnnoise-wasm": "github:jitsi/rnnoise-wasm#566a16885897704d6e6d67a1d5ac5d39781db2af", "rtcstats": "github:jitsi/rtcstats#v6.2.0", "string-similarity": "^4.0.4", "styled-components": "3.4.9", "toastify-react-native": "^3.1.0", "tss-react": "^4.9.4", "type-fest": "^4.18.2", "util": "0.12.1", "uuid": "3.1.0", "wasm-check": "2.0.1", "windows-iana": "^3.1.0", "xmldom": "^0.6.0", "zxcvbn": "4.4.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/eslint-parser": "7.16.0", "@babel/plugin-proposal-class-properties": "7.1.0", "@babel/plugin-proposal-export-default-from": "7.16.0", "@babel/plugin-proposal-export-namespace-from": "7.0.0", "@babel/plugin-proposal-nullish-coalescing-operator": "7.4.4", "@babel/plugin-proposal-optional-chaining": "7.2.0", "@babel/plugin-transform-flow-strip-types": "7.0.0", "@babel/preset-env": "^7.20.0", "@babel/preset-flow": "7.0.0", "@babel/preset-react": "7.0.0", "@babel/runtime": "^7.20.0", "@jitsi/eslint-config": "^4.1.4", "@react-native/babel-preset": "0.74.81", "@react-native/eslint-config": "0.74.81", "@react-native/metro-config": "0.74.81", "@react-native/typescript-config": "0.74.81", "@redux-devtools/core": "^3.9.0", "@types/core-js": "^2.5.5", "@types/js-md5": "0.4.3", "@types/lodash": "4.14.182", "@types/react": "^18.2.6", "@types/react-native": "0.68.1", "@types/react-redux": "7.1.24", "@types/react-test-renderer": "^18.0.0", "@types/react-window": "1.8.5", "@types/unorm": "1.3.28", "@types/uuid": "8.3.4", "@types/zxcvbn": "4.4.1", "@typescript-eslint/eslint-plugin": "^5.30.5", "@typescript-eslint/parser": "^5.30.4", "babel-eslint": "^10.1.0", "babel-jest": "^29.6.3", "babel-loader": "8.2.3", "babel-plugin-optional-require": "0.3.1", "circular-dependency-plugin": "5.2.0", "clean-css-cli": "4.3.0", "css-loader": "3.6.0", "eslint": "^8.19.0", "eslint-config-jitsi": "github:jitsi/eslint-config-jitsi#1.0.3", "eslint-plugin-flowtype": "8.0.3", "eslint-plugin-import": "^2.25.2", "eslint-plugin-jsdoc": "^50.3.1", "eslint-plugin-react": "^7.37.1", "eslint-plugin-react-native": "3.3.0", "eslint-plugin-typescript-sort-keys": "2.1.0", "expose-loader": "0.7.5", "imports-loader": "0.7.1", "jest": "^29.6.3", "jetifier": "1.6.4", "metro-react-native-babel-preset": "0.77.0", "patch-package": "6.4.7", "prettier": "2.8.8", "process": "0.11.10", "react-test-renderer": "18.2.0", "react-native-google-mobile-ads": "~14.7.2", "sass": "1.26.8", "string-replace-loader": "3.0.3", "style-loader": "3.3.1", "traverse": "0.6.6", "ts-loader": "9.2.6", "typescript": "5.0.4", "unorm": "1.6.0", "webpack": "5.57.1", "webpack-bundle-analyzer": "4.4.2", "webpack-cli": "4.10.0", "webpack-dev-server": "4.7.3"}, "engines": {"node": ">=18", "npm": ">=6.0.0"}, "license": "Apache-2.0", "scripts": {"lint": "eslint .", "lint-fix": "eslint . --fix", "postinstall": "patch-package --error-on-fail && jetify", "validate": "npm ls", "dev": "export WEBPACK_DEV_SERVER_PROXY_TARGET=https://meethour.io/ && make dev", "start": "make dev", "build": "make && make source-package", "build:dev": "make && make dev-source-package", "build:nextjs": "make && make next-source-package", "build:nextjs-dev": "make && make dev-next-source-package", "start:mobile": "react-native start", "start:mobile-rc": "react-native start --reset-cache", "start:android": "npx react-native run-android", "start:ios": "npx react-native run-ios", "release:ios": "sh ios/scripts/release-sdk.sh", "release:android": "sh android/scripts/release-sdk.sh ~/.m2/repository", "build:android": "react-native bundle --entry-file=index.android.js --bundle-output=./android/app/build/generated/assets/createBundleReleaseJsAndAssets/index.android.meethour.bundle --assets-dest=./android/app/src/main/res --dev=false --platform=android", "build:ios": "react-native bundle --entry-file=index.ios.js --bundle-output=./ios/main.jsbundle --dev=false --platform=ios"}, "browser": {"jQuery-Impromptu": "jQuery-Impromptu/src/jquery-impromptu.js"}, "packageManager": "yarn@3.6.4"}